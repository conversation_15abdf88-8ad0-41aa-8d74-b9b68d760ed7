// Helper functions for SuperUser Dashboard

export const getPriorityColor = (priority) => {
  switch (priority) {
    case "Critical":
      return "bg-red-500";
    case "High":
      return "bg-orange-500";
    case "Medium":
      return "bg-yellow-500";
    case "Low":
      return "bg-green-500";
    default:
      return "bg-gray-500";
  }
};

export const getRiskColor = (risk) => {
  switch (risk) {
    case "High":
      return "text-red-600 bg-red-100";
    case "Medium":
      return "text-yellow-600 bg-yellow-100";
    case "Low":
      return "text-green-600 bg-green-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
};

export const getStatusColor = (status) => {
  switch (status) {
    case "Pending Approval":
      return "bg-yellow-100 text-yellow-800";
    case "In Progress":
      return "bg-blue-100 text-blue-800";
    case "Resolved":
      return "bg-green-100 text-green-800";
    case "Rejected":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const getCategoryColor = (category) => {
  switch (category) {
    case "Technical":
      return "text-blue-700 bg-blue-100 border-blue-200";
    case "Feature Request":
      return "text-purple-700 bg-purple-100 border-purple-200";
    case "Security":
      return "text-red-700 bg-red-100 border-red-200";
    case "Documentation":
      return "text-green-700 bg-green-100 border-green-200";
    case "Billing":
      return "text-orange-700 bg-orange-100 border-orange-200";
    case "Account":
      return "text-indigo-700 bg-indigo-100 border-indigo-200";
    default:
      return "text-gray-700 bg-gray-100 border-gray-200";
  }
};
