// Dummy ticket data for the Ticket Approval Center
export const ticketData = [
  {
    id: "TK-005",
    subject: "API Rate Limiting Issues",
    customer: "TechCorp Ltd",
    category: "Technical",
    priority: "High",
    created: "2025-05-26 09:30",
    description: "Our API calls are being rate limited unexpectedly...",
    attachments: 2,
    riskLevel: "Medium",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "John Doe",
    lastUpdate: "2025-05-26 09:30",
  },
  {
    id: "TK-006",
    subject: "Bulk Data Export Feature",
    customer: "DataSync Inc",
    category: "Feature Request",
    priority: "Medium",
    created: "2025-05-26 10:15",
    description: "Need ability to export large datasets in CSV format...",
    attachments: 1,
    riskLevel: "Low",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "<PERSON>",
    lastUpdate: "2025-05-26 10:15",
  },
  {
    id: "TK-007",
    subject: "Security Vulnerability Report",
    customer: "SecureApp Solutions",
    category: "Security",
    priority: "Critical",
    created: "2025-05-26 11:00",
    description: "Potential XSS vulnerability found in user dashboard...",
    attachments: 3,
    riskLevel: "High",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "Mike <PERSON>",
    lastUpdate: "2025-05-26 11:00",
  },
  {
    id: "TK-008",
    subject: "Integration Documentation",
    customer: "DevTeam Alpha",
    category: "Documentation",
    priority: "Low",
    created: "2025-05-26 11:45",
    description: "Missing documentation for webhook integration...",
    attachments: 0,
    riskLevel: "Low",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "Sarah Wilson",
    lastUpdate: "2025-05-26 11:45",
  },
  {
    id: "TK-009",
    subject: "Billing Discrepancy Issue",
    customer: "Enterprise Corp",
    category: "Billing",
    priority: "High",
    created: "2025-05-26 12:30",
    description: "Incorrect charges appearing on monthly invoice...",
    attachments: 1,
    riskLevel: "Medium",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "Tom Brown",
    lastUpdate: "2025-05-26 12:30",
  },
  {
    id: "TK-010",
    subject: "Account Access Problems",
    customer: "StartupXYZ",
    category: "Account",
    priority: "Medium",
    created: "2025-05-26 13:15",
    description: "Unable to access admin panel after recent update...",
    attachments: 0,
    riskLevel: "Low",
    status: "Pending Approval",
    createdBy: "customer",
    assignedAgent: "Lisa Davis",
    lastUpdate: "2025-05-26 13:15",
  },
  // Approved tickets
  {
    id: "TK-001",
    subject: "Database Performance Optimization",
    customer: "TechFlow Inc",
    category: "Technical",
    priority: "High",
    created: "2025-05-25 14:20",
    description:
      "Database queries are running slowly affecting user experience...",
    attachments: 2,
    riskLevel: "Medium",
    status: "Approved",
    createdBy: "customer",
    assignedAgent: "John Doe",
    lastUpdate: "2025-05-25 15:30",
    approvalNotes: "Approved for immediate investigation",
  },
  {
    id: "TK-002",
    subject: "New Dashboard Feature Request",
    customer: "InnovateCorp",
    category: "Feature Request",
    priority: "Medium",
    created: "2025-05-25 16:45",
    description: "Request for custom dashboard widgets...",
    attachments: 1,
    riskLevel: "Low",
    status: "Approved",
    createdBy: "customer",
    assignedAgent: "Jane Smith",
    lastUpdate: "2025-05-25 17:00",
    approvalNotes: "Feature approved for next sprint",
  },
  // Rejected tickets
  {
    id: "TK-003",
    subject: "Unrealistic Feature Request",
    customer: "DreamBig Ltd",
    category: "Feature Request",
    priority: "Low",
    created: "2025-05-24 10:30",
    description: "Request for AI-powered mind reading feature...",
    attachments: 0,
    riskLevel: "Low",
    status: "Rejected",
    createdBy: "customer",
    assignedAgent: "Mike Johnson",
    lastUpdate: "2025-05-24 11:00",
    rejectionReason: "Feature not technically feasible",
  },
  // Superuser created tickets
  {
    id: "TK-011",
    subject: "System Maintenance Schedule",
    customer: "Internal",
    category: "Technical",
    priority: "Medium",
    created: "2025-05-26 08:00",
    description: "Scheduled maintenance for server upgrades...",
    attachments: 1,
    riskLevel: "Low",
    status: "Approved",
    createdBy: "superuser",
    assignedAgent: "System Admin",
    lastUpdate: "2025-05-26 08:15",
    approvalNotes: "Auto-approved internal ticket",
    canClose: true,
    canDelete: false,
    canReopen: false,
    canResubmit: false,
  },
  {
    id: "TK-012",
    subject: "Security Audit Report",
    customer: "Internal",
    category: "Security",
    priority: "High",
    created: "2025-05-26 09:00",
    description: "Monthly security audit findings and recommendations...",
    attachments: 3,
    riskLevel: "Medium",
    status: "Pending Approval",
    createdBy: "superuser",
    assignedAgent: "Security Team",
    lastUpdate: "2025-05-26 09:00",
    canClose: false,
    canDelete: false,
    canReopen: false,
    canResubmit: false,
  },
  {
    id: "TK-013",
    subject: "User Training Documentation",
    customer: "Internal",
    category: "Documentation",
    priority: "Low",
    created: "2025-05-25 14:30",
    description: "Updated user training materials for new features...",
    attachments: 2,
    riskLevel: "Low",
    status: "Approved",
    createdBy: "superuser",
    assignedAgent: "Training Team",
    lastUpdate: "2025-05-25 15:00",
    approvalNotes: "Documentation approved for publication",
    canClose: true,
    canDelete: false,
    canReopen: false,
    canResubmit: false,
  },
  {
    id: "TK-014",
    subject: "Performance Monitoring Setup",
    customer: "Internal",
    category: "Technical",
    priority: "High",
    created: "2025-05-26 10:30",
    description:
      "Setting up comprehensive performance monitoring for production systems...",
    attachments: 1,
    riskLevel: "Medium",
    status: "In Progress",
    createdBy: "superuser",
    assignedAgent: "DevOps Team",
    lastUpdate: "2025-05-26 11:00",
    approvalNotes: "Auto-approved internal ticket",
    canClose: true,
    canDelete: false,
    canReopen: false,
    canResubmit: false,
  },
  {
    id: "TK-015",
    subject: "Database Backup Verification",
    customer: "Internal",
    category: "Technical",
    priority: "Medium",
    created: "2025-05-26 12:00",
    description: "Verify integrity of automated database backups...",
    attachments: 0,
    riskLevel: "Low",
    status: "Seen",
    createdBy: "superuser",
    assignedAgent: "Database Admin",
    lastUpdate: "2025-05-26 12:30",
    approvalNotes: "Auto-approved internal ticket",
    canClose: true,
    canDelete: false,
    canReopen: false,
    canResubmit: false,
  },
];

// Customer Dashboard Ticket Data
export const customerTicketData = [
  {
    id: "TK-001",
    subject: "Login Issues",
    category: "Technical",
    status: "In Progress",
    priority: "High",
    created: "2025-05-25 09:30 AM",
    createdTimestamp: new Date("2025-05-25T09:30:00"),
    lastUpdate: "2 hours ago",
    agent: "John Smith",
    agentId: "agent_001",
    canDelete: false,
    canClose: true,
    canReopen: false,
    canResubmit: false,
    seenByAgent: true,
    description:
      "I'm experiencing login issues with my account. When I try to log in, I get an error message saying 'Invalid credentials' even though I'm using the correct username and password. This started happening after the recent system update.",
    rejectionReason: null,
    approvalNotes: "Approved for technical investigation",
    isActive: true,
  },
  {
    id: "TK-002",
    subject: "Feature Request - Dark Mode",
    category: "Feature Request",
    status: "Pending Approval",
    priority: "Medium",
    created: "2025-05-26 02:15 PM",
    createdTimestamp: new Date("2025-05-26T14:15:00"),
    lastUpdate: "5 minutes ago",
    agent: null,
    agentId: null,
    canDelete: true, // Can delete within 10 minutes if not seen
    canClose: false,
    canReopen: false,
    canResubmit: false,
    seenByAgent: false,
    description:
      "It would be great to have a dark mode option in the application. Many users prefer dark themes, especially when working in low-light environments. This would improve user experience and reduce eye strain.",
    rejectionReason: null,
    approvalNotes: null,
    isActive: false,
  },
  {
    id: "TK-003",
    subject: "Billing Question",
    category: "Billing",
    status: "Resolved",
    priority: "Low",
    created: "2025-05-20 10:00 AM",
    createdTimestamp: new Date("2025-05-20T10:00:00"),
    lastUpdate: "3 days ago",
    agent: "Sarah Johnson",
    agentId: "agent_002",
    canDelete: false,
    canClose: false,
    canReopen: true, // Can continue chat after resolution
    canResubmit: false,
    seenByAgent: true,
    description:
      "I have a question about my recent billing statement. There seems to be a charge that I don't recognize. Could you please help me understand what this charge is for?",
    rejectionReason: null,
    approvalNotes: "Standard billing inquiry - approved",
    isActive: false,
  },
  {
    id: "TK-004",
    subject: "API Documentation",
    category: "Documentation",
    status: "Rejected",
    priority: "Medium",
    created: "2025-05-24 11:45 AM",
    createdTimestamp: new Date("2025-05-24T11:45:00"),
    lastUpdate: "1 day ago",
    agent: null,
    agentId: null,
    canDelete: false,
    canClose: false,
    canReopen: false,
    canResubmit: true, // Can modify and resubmit
    seenByAgent: true,
    description:
      "The API documentation seems to be missing some important endpoints. Specifically, I can't find documentation for the user management endpoints that were mentioned in the changelog.",
    rejectionReason:
      "Please be more specific about which endpoints are missing. Include the exact endpoint names and expected functionality.",
    approvalNotes: null,
    isActive: false,
  },
  {
    id: "TK-005",
    subject: "Account Security Concern",
    category: "Security",
    status: "Approved",
    priority: "High",
    created: "2025-05-26 08:20 AM",
    createdTimestamp: new Date("2025-05-26T08:20:00"),
    lastUpdate: "30 minutes ago",
    agent: "Mike Wilson",
    agentId: "agent_003",
    canDelete: false,
    canClose: true,
    canReopen: false,
    canResubmit: false,
    seenByAgent: true,
    description:
      "I noticed some unusual login attempts on my account from unknown IP addresses. I'm concerned about the security of my account and would like to review recent activity.",
    rejectionReason: null,
    approvalNotes: "Security concern - escalated to security team",
    isActive: true,
  },
  {
    id: "TK-006",
    subject: "Mobile App Crash",
    category: "Technical",
    status: "Seen",
    priority: "Medium",
    created: "2025-05-26 01:00 PM",
    createdTimestamp: new Date("2025-05-26T13:00:00"),
    lastUpdate: "15 minutes ago",
    agent: "John Smith",
    agentId: "agent_001",
    canDelete: false,
    canClose: true,
    canReopen: false,
    canResubmit: false,
    seenByAgent: true,
    description:
      "The mobile app keeps crashing when I try to access the dashboard. This happens consistently on both iOS and Android devices.",
    rejectionReason: null,
    approvalNotes: "Technical issue confirmed - investigating",
    isActive: true,
  },
];

// Filter options for dropdowns (SuperUser Dashboard)
export const filterOptions = {
  categories: [
    "Technical",
    "Feature Request",
    "Security",
    "Documentation",
    "Billing",
    "Account",
  ],
  priorities: ["Low", "Medium", "High", "Critical"],
  riskLevels: ["Low", "Medium", "High"],
  statuses: ["Pending Approval", "Approved", "Rejected"],
  customers: [...new Set(ticketData.map((ticket) => ticket.customer))],
  attachmentCounts: ["0", "1", "2+"],
};

// Filter options for Customer Dashboard
export const customerFilterOptions = {
  categories: [
    "Technical",
    "Feature Request",
    "Billing",
    "Documentation",
    "Security",
  ],
  statuses: [
    "Pending Approval",
    "In Progress",
    "Seen",
    "Approved",
    "Resolved",
    "Rejected",
  ],
  priorities: ["High", "Medium", "Low"],
  agents: ["John Smith", "Sarah Johnson", "Mike Wilson", "Unassigned"],
};

// Helper functions for data filtering
export const filterTickets = (tickets, filters) => {
  if (!filters || Object.keys(filters).length === 0) {
    return tickets;
  }

  return tickets.filter((ticket) => {
    // Category filter
    if (filters.category && ticket.category !== filters.category) return false;

    // Priority filter
    if (filters.priority && ticket.priority !== filters.priority) return false;

    // Risk level filter
    if (filters.riskLevel && ticket.riskLevel !== filters.riskLevel)
      return false;

    // Status filter
    if (filters.status && ticket.status !== filters.status) return false;

    // Customer filter
    if (
      filters.customer &&
      !ticket.customer.toLowerCase().includes(filters.customer.toLowerCase())
    )
      return false;

    // Attachment count filter
    if (filters.attachmentCount) {
      if (filters.attachmentCount === "0" && ticket.attachments !== 0)
        return false;
      if (filters.attachmentCount === "1" && ticket.attachments !== 1)
        return false;
      if (filters.attachmentCount === "2+" && ticket.attachments < 2)
        return false;
    }

    // Date range filter
    if (filters.dateFrom || filters.dateTo) {
      const ticketDate = new Date(ticket.created);
      if (filters.dateFrom && ticketDate < new Date(filters.dateFrom))
        return false;
      if (filters.dateTo && ticketDate > new Date(filters.dateTo)) return false;
    }

    // Search filter (ID or keyword)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableFields = [
        ticket.id,
        ticket.subject,
        ticket.description,
        ticket.customer,
      ]
        .join(" ")
        .toLowerCase();

      if (!searchableFields.includes(searchTerm)) return false;
    }

    return true;
  });
};

// Get tickets by view type (SuperUser Dashboard)
export const getTicketsByView = (viewType, currentUser = "superuser") => {
  switch (viewType) {
    case "all":
      return ticketData;
    case "unapproved":
      return ticketData.filter(
        (ticket) => ticket.status === "Pending Approval"
      );
    case "myTickets":
      return ticketData.filter((ticket) => ticket.createdBy === "superuser");
    default:
      return ticketData;
  }
};

// Customer Dashboard specific functions
export const filterCustomerTickets = (tickets, filters) => {
  if (!filters || Object.keys(filters).length === 0) {
    return tickets;
  }

  return tickets.filter((ticket) => {
    // Category filter
    if (filters.category && ticket.category !== filters.category) return false;

    // Status filter
    if (filters.status && ticket.status !== filters.status) return false;

    // Priority filter
    if (filters.priority && ticket.priority !== filters.priority) return false;

    // Agent filter
    if (filters.agent) {
      const ticketAgent = ticket.agent || "Unassigned";
      if (ticketAgent !== filters.agent) return false;
    }

    return true;
  });
};

// Get customer tickets by tab
export const getCustomerTicketsByTab = (tickets, activeTab) => {
  switch (activeTab) {
    case "all":
      return tickets;
    case "pending":
      return tickets.filter((ticket) => ticket.status === "Pending Approval");
    case "active":
      return tickets.filter((ticket) =>
        ["In Progress", "Seen", "Approved"].includes(ticket.status)
      );
    case "resolved":
      return tickets.filter((ticket) => ticket.status === "Resolved");
    case "rejected":
      return tickets.filter((ticket) => ticket.status === "Rejected");
    default:
      return tickets;
  }
};
