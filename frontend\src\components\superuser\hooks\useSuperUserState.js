import { useMemo, useState } from "react";
import { filterOptions, filterTickets, getTicketsByView } from "../../../data/data";

export const useSuperUserState = () => {
  // View management
  const [activeView, setActiveView] = useState("all"); // all, unapproved, myTickets

  // Selection and modal state
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // Dynamic filter state
  const [pendingFilters, setPendingFilters] = useState({});
  const [appliedFilters, setAppliedFilters] = useState({
    all: {},
    unapproved: {},
    myTickets: {},
  });
  const [activeFilterColumn, setActiveFilterColumn] = useState(null);

  // Filter management functions
  const handleFilterSelect = (column, value) => {
    setPendingFilters((prev) => ({
      ...prev,
      [column]: value,
    }));
  };

  const handleRemoveFilter = (column) => {
    setPendingFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[column];
      return newFilters;
    });
  };

  const handleApplyFilters = () => {
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: pendingFilters,
    }));
  };

  const handleClearAllFilters = () => {
    setPendingFilters({});
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: {},
    }));
  };

  // Get tickets for current view and apply filters
  const currentViewTickets = useMemo(() => {
    const viewTickets = getTicketsByView(activeView);
    const currentFilters = appliedFilters[activeView] || {};
    return filterTickets(viewTickets, currentFilters);
  }, [activeView, appliedFilters]);

  // Ticket selection functions
  const handleTicketSelection = (ticketId) => {
    setSelectedTickets((prev) =>
      prev.includes(ticketId)
        ? prev.filter((id) => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAllTickets = (checked) => {
    if (checked) {
      setSelectedTickets(currentViewTickets.map((t) => t.id));
    } else {
      setSelectedTickets([]);
    }
  };

  // Modal management functions
  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);

    // If on "Unapproved Tickets" tab, show approval modal for review
    // For all other tabs ("All Tickets", "My Tickets"), show ticket details modal (read-only)
    if (activeView === "unapproved") {
      setShowApprovalModal(true);
    } else {
      setShowDetailModal(true);
    }
  };

  const closeModals = () => {
    setShowApprovalModal(false);
    setShowDetailModal(false);
    setShowCreateModal(false);
    setSelectedTicket(null);
  };

  // Get view statistics
  const getViewStats = () => {
    const allTickets = getTicketsByView("all");
    const unapprovedTickets = getTicketsByView("unapproved");
    const myTickets = getTicketsByView("myTickets");

    return {
      all: allTickets.length,
      unapproved: unapprovedTickets.length,
      myTickets: myTickets.length,
    };
  };

  const viewStats = getViewStats();

  return {
    // State
    activeView,
    selectedTickets,
    showApprovalModal,
    showDetailModal,
    showCreateModal,
    selectedTicket,
    pendingFilters,
    appliedFilters,
    activeFilterColumn,
    currentViewTickets,
    viewStats,

    // Setters
    setActiveView,
    setSelectedTickets,
    setShowApprovalModal,
    setShowDetailModal,
    setShowCreateModal,
    setSelectedTicket,
    setActiveFilterColumn,

    // Handlers
    handleFilterSelect,
    handleRemoveFilter,
    handleApplyFilters,
    handleClearAllFilters,
    handleTicketSelection,
    handleSelectAllTickets,
    handleTicketClick,
    closeModals,
  };
};
