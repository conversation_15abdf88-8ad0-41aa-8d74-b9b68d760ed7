import { Filter } from "lucide-react";
import { ColumnFilter } from "./DynamicFilter";
import { filterOptions } from "../../data/data";

const SuperUserTableHeaders = ({
  activeView,
  currentViewTickets,
  activeFilterColumn,
  pendingFilters,
  onFilterColumnChange,
  onFilterSelect,
}) => {
  if (activeView === "myTickets") {
    return (
      <>
        {/* Customer Dashboard style headers for My Tickets */}
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Ticket</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "category" ? null : "category"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Category"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "category" && (
            <ColumnFilter
              column="category"
              options={filterOptions.categories}
              value={pendingFilters.category}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Status</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "status" ? null : "status"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Status"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "status" && (
            <ColumnFilter
              column="status"
              options={[
                "Pending Approval",
                "In Progress",
                "Resolved",
                "Rejected",
              ]}
              value={pendingFilters.status}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Priority</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "priority" ? null : "priority"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Priority"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "priority" && (
            <ColumnFilter
              column="priority"
              options={filterOptions.priorities}
              value={pendingFilters.priority}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Assigned Agent
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Last Update
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </>
    );
  } else {
    return (
      <>
        {/* Original Super User headers for All Tickets and Unapproved Tickets */}
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Select
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Ticket Details</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "ticketDetails"
                    ? null
                    : "ticketDetails"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Ticket Details"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "ticketDetails" && (
            <ColumnFilter
              column="ticketDetails"
              options={[...new Set(currentViewTickets.map((t) => t.id))]}
              value={pendingFilters.ticketDetails}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Customer</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "customer" ? null : "customer"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Customer"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "customer" && (
            <ColumnFilter
              column="customer"
              options={filterOptions.customers}
              value={pendingFilters.customer}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Category</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "category" ? null : "category"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Category"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "category" && (
            <ColumnFilter
              column="category"
              options={filterOptions.categories}
              value={pendingFilters.category}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Priority</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "priority" ? null : "priority"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Priority"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "priority" && (
            <ColumnFilter
              column="priority"
              options={filterOptions.priorities}
              value={pendingFilters.priority}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative">
          <div className="flex items-center justify-between">
            <span>Risk Level</span>
            <button
              onClick={() =>
                onFilterColumnChange(
                  activeFilterColumn === "riskLevel" ? null : "riskLevel"
                )
              }
              className="ml-1 p-1 hover:bg-gray-200 rounded transition-colors"
              title="Filter Risk Level"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
          {activeFilterColumn === "riskLevel" && (
            <ColumnFilter
              column="riskLevel"
              options={filterOptions.riskLevels}
              value={pendingFilters.riskLevel}
              onChange={onFilterSelect}
              onClose={() => onFilterColumnChange(null)}
            />
          )}
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Created
        </th>
        <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </>
    );
  }
};

export default SuperUserTableHeaders;
