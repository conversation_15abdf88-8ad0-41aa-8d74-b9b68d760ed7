{"hash": "8684828f", "configHash": "43f130b7", "lockfileHash": "179cf6be", "browserHash": "74a57fe2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a3892694", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6191272a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "25cea0d3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5f263238", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b0fb0f4c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e2e91982", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "2b6bf67e", "needsInterop": false}}, "chunks": {"chunk-NYHBQTAG": {"file": "chunk-NYHBQTAG.js"}, "chunk-P5XWQMHZ": {"file": "chunk-P5XWQMHZ.js"}}}