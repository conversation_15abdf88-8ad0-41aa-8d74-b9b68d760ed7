import { Clock, List, UserCheck } from "lucide-react";

const SuperUserTabs = ({ activeView, onViewChange, viewStats }) => {
  const tabs = [
    {
      key: "all",
      label: "All Tickets",
      count: viewStats.all,
      icon: List,
    },
    {
      key: "unapproved",
      label: "Unapproved Tickets",
      count: viewStats.unapproved,
      icon: Clock,
    },
    {
      key: "myTickets",
      label: "My Tickets",
      count: viewStats.myTickets,
      icon: UserCheck,
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm mb-3">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {tabs.map((view) => {
            const Icon = view.icon;
            return (
              <button
                key={view.key}
                onClick={() => onViewChange(view.key)}
                className={`flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-xs transition-colors ${
                  activeView === view.key
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{view.label}</span>
                <span
                  className={`px-2 py-0.5 text-xs rounded-full ${
                    activeView === view.key
                      ? "bg-blue-100 text-blue-600"
                      : "bg-gray-100 text-gray-600"
                  }`}
                >
                  {view.count}
                </span>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default SuperUserTabs;
