import {
  AlertTriangle,
  Clock,
  FileText,
  Shield,
  TrendingUp,
} from "lucide-react";

const SuperUserStats = ({ tickets }) => {
  const stats = {
    total: tickets.length,
    critical: tickets.filter((t) => t.priority === "Critical").length,
    high: tickets.filter((t) => t.priority === "High").length,
    highRisk: tickets.filter((t) => t.riskLevel === "High").length,
    mediumRisk: tickets.filter((t) => t.riskLevel === "Medium").length,
    technical: tickets.filter((t) => t.category === "Technical").length,
    security: tickets.filter((t) => t.category === "Security").length,
    withAttachments: tickets.filter((t) => t.attachments > 0).length,
    avgResponseTime: "2.3 hours", // This would come from your backend
    approvalRate: "94%", // This would come from your backend
  };

  const statCards = [
    {
      title: "Total Pending",
      value: stats.total,
      icon: Clock,
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
      textColor: "text-blue-900",
    },
    {
      title: "Critical Priority",
      value: stats.critical,
      icon: AlertTriangle,
      bgColor: "bg-red-100",
      iconColor: "text-red-600",
      textColor: "text-red-900",
    },
    {
      title: "High Risk",
      value: stats.highRisk,
      icon: Shield,
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      textColor: "text-orange-900",
    },
    {
      title: "Security Issues",
      value: stats.security,
      icon: Shield,
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
      textColor: "text-purple-900",
    },
    {
      title: "With Attachments",
      value: stats.withAttachments,
      icon: FileText,
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
      textColor: "text-green-900",
    },
    {
      title: "Avg Response",
      value: stats.avgResponseTime,
      icon: TrendingUp,
      bgColor: "bg-indigo-100",
      iconColor: "text-indigo-600",
      textColor: "text-indigo-900",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-4">
      {statCards.map((card, index) => {
        const Icon = card.icon;
        return (
          <div
            key={index}
            className="bg-white rounded-lg p-3 shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className={`p-2 ${card.bgColor} rounded-lg`}>
                <Icon className={`w-4 h-4 ${card.iconColor}`} />
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">
                  {card.title}
                </p>
                <p className={`text-sm font-bold ${card.textColor}`}>
                  {card.value}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SuperUserStats;
