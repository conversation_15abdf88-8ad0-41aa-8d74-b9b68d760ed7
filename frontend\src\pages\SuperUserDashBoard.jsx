import { FilterBar } from "../components/superuser/DynamicFilter";
import SuperUserHeader from "../components/superuser/SuperUserHeader";
import SuperUserModals from "../components/superuser/SuperUserModals";
import SuperUserTableContainer from "../components/superuser/SuperUserTableContainer";
import SuperUserTabs from "../components/superuser/SuperUserTabs";
import { useSuperUserState } from "../components/superuser/hooks/useSuperUserState";

const SuperUserDashboard = () => {
  // Use the custom hook for state management
  const {
    // State
    activeView,
    selectedTickets,
    showApprovalModal,
    showDetailModal,
    showCreateModal,
    selectedTicket,
    pendingFilters,
    activeFilterColumn,
    currentViewTickets,
    viewStats,

    // Setters
    setActiveView,
    setShowCreateModal,
    setActiveFilterColumn,

    // Handlers
    handleFilterSelect,
    handleRemoveFilter,
    handleApplyFilters,
    handleClearAllFilters,
    handleTicketSelection,
    handleSelectAllTickets,
    handleTicketClick,
    closeModals,
  } = useSuperUserState();

  // Business logic handlers
  const handleBulkApprove = () => {
    console.log("Bulk approving tickets:", selectedTickets);
    // Clear selection after bulk action
  };

  const handleBulkReject = () => {
    console.log("Bulk rejecting tickets:", selectedTickets);
    // Clear selection after bulk action
  };

  const handleBulkDelete = () => {
    console.log("Bulk deleting tickets:", selectedTickets);
    // Here you would typically make an API call to delete the tickets
    // Clear selection after bulk action
  };

  const handleApprove = (ticketId, approvalNotes) => {
    console.log("Approving ticket:", ticketId, "with notes:", approvalNotes);
    // Here you would typically make an API call to approve the ticket
    // Update ticket status to "Approved" and add approval notes
  };

  const handleReject = (ticketId, rejectionReason) => {
    console.log("Rejecting ticket:", ticketId, "with reason:", rejectionReason);
    // Here you would typically make an API call to reject the ticket
    // Update ticket status to "Rejected" and add rejection reason
  };

  const handleCreateTicket = async (ticketData) => {
    console.log("Creating new ticket as super user:", ticketData);
    // Create ticket with "Approved" status since super user created it
    // Max 25MB attachments, max 5 files
    setShowCreateModal(false);
  };

  const handleTicketAction = (action, ticketId) => {
    console.log("Ticket action:", action, "for ticket:", ticketId);
    // Handle ticket actions like close, delete, resubmit, etc.
    // This is used by the customer TicketDetailModal
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <SuperUserHeader
        selectedTickets={selectedTickets}
        activeView={activeView}
        onCreateTicket={() => setShowCreateModal(true)}
        onBulkApprove={handleBulkApprove}
        onBulkReject={handleBulkReject}
        onBulkDelete={handleBulkDelete}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        {/* View Tabs */}
        <SuperUserTabs
          activeView={activeView}
          onViewChange={setActiveView}
          viewStats={viewStats}
        />

        {/* Dynamic Filter Bar */}
        <FilterBar
          filters={pendingFilters}
          onRemoveFilter={handleRemoveFilter}
          onApplyFilters={handleApplyFilters}
          onClearAll={handleClearAllFilters}
        />

        {/* Tickets Table */}
        <SuperUserTableContainer
          activeView={activeView}
          currentViewTickets={currentViewTickets}
          selectedTickets={selectedTickets}
          activeFilterColumn={activeFilterColumn}
          pendingFilters={pendingFilters}
          onSelectAllTickets={handleSelectAllTickets}
          onFilterColumnChange={setActiveFilterColumn}
          onFilterSelect={handleFilterSelect}
          onTicketSelection={handleTicketSelection}
          onTicketClick={handleTicketClick}
          onTicketAction={handleTicketAction}
          onApprove={handleApprove}
          onReject={handleReject}
        />
      </div>

      {/* Modals */}
      <SuperUserModals
        showCreateModal={showCreateModal}
        showApprovalModal={showApprovalModal}
        showDetailModal={showDetailModal}
        selectedTicket={selectedTicket}
        onCloseModals={closeModals}
        onCreateTicket={handleCreateTicket}
        onApprove={handleApprove}
        onReject={handleReject}
        onTicketAction={handleTicketAction}
      />
    </div>
  );
};

export default SuperUserDashboard;
