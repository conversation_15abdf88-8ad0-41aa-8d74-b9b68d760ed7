import { useMemo, useState } from "react";
import { filterTickets, getTicketsByView } from "../../../data/data";

export const useSuperUserDashboard = () => {
  // View management
  const [activeView, setActiveView] = useState("all"); // all, unapproved, myTickets

  // Selection and modal state
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // Dynamic filter state
  const [pendingFilters, setPendingFilters] = useState({});
  const [appliedFilters, setAppliedFilters] = useState({
    all: {},
    unapproved: {},
    myTickets: {},
  });
  const [activeFilterColumn, setActiveFilterColumn] = useState(null);

  // Filter management functions
  const handleFilterSelect = (column, value) => {
    setPendingFilters((prev) => ({
      ...prev,
      [column]: value,
    }));
  };

  const handleRemoveFilter = (column) => {
    setPendingFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[column];
      return newFilters;
    });
  };

  const handleApplyFilters = () => {
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: pendingFilters,
    }));
  };

  const handleClearAllFilters = () => {
    setPendingFilters({});
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: {},
    }));
  };

  // Get tickets for current view and apply filters
  const currentViewTickets = useMemo(() => {
    const viewTickets = getTicketsByView(activeView);
    const currentFilters = appliedFilters[activeView] || {};
    return filterTickets(viewTickets, currentFilters);
  }, [activeView, appliedFilters]);

  // Ticket selection handlers
  const handleTicketSelection = (ticketId) => {
    setSelectedTickets((prev) =>
      prev.includes(ticketId)
        ? prev.filter((id) => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAllTickets = (tickets) => {
    setSelectedTickets(tickets.map((t) => t.id));
  };

  const handleDeselectAllTickets = () => {
    setSelectedTickets([]);
  };

  // Ticket click handler
  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);

    // If on "Unapproved Tickets" tab, show approval modal for review
    // For all other tabs ("All Tickets", "My Tickets"), show ticket details modal (read-only)
    if (activeView === "unapproved") {
      setShowApprovalModal(true);
    } else {
      setShowDetailModal(true);
    }
  };

  // Bulk action handlers
  const handleBulkApprove = () => {
    console.log("Bulk approving tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleBulkReject = () => {
    console.log("Bulk rejecting tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleBulkDelete = () => {
    console.log("Bulk deleting tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  // Individual ticket action handlers
  const handleApprove = (ticketId, approvalNotes) => {
    console.log("Approving ticket:", ticketId, "with notes:", approvalNotes);
  };

  const handleReject = (ticketId, rejectionReason) => {
    console.log("Rejecting ticket:", ticketId, "with reason:", rejectionReason);
  };

  const handleCreateTicket = async (ticketData) => {
    console.log("Creating new ticket as super user:", ticketData);
    setShowCreateModal(false);
  };

  const handleTicketAction = (action, ticketId) => {
    console.log("Ticket action:", action, "for ticket:", ticketId);
  };

  // Modal handlers
  const closeApprovalModal = () => {
    setShowApprovalModal(false);
    setSelectedTicket(null);
  };

  const closeDetailModal = () => {
    setShowDetailModal(false);
    setSelectedTicket(null);
  };

  const openCreateModal = () => {
    setShowCreateModal(true);
  };

  const closeCreateModal = () => {
    setShowCreateModal(false);
  };

  // Get view statistics
  const getViewStats = () => {
    const allTickets = getTicketsByView("all");
    const unapprovedTickets = getTicketsByView("unapproved");
    const myTickets = getTicketsByView("myTickets");

    return {
      all: allTickets.length,
      unapproved: unapprovedTickets.length,
      myTickets: myTickets.length,
    };
  };

  const viewStats = getViewStats();

  return {
    // State
    activeView,
    selectedTickets,
    showApprovalModal,
    showDetailModal,
    showCreateModal,
    selectedTicket,
    pendingFilters,
    appliedFilters,
    activeFilterColumn,
    currentViewTickets,
    viewStats,

    // Setters
    setActiveView,
    setActiveFilterColumn,

    // Handlers
    handleFilterSelect,
    handleRemoveFilter,
    handleApplyFilters,
    handleClearAllFilters,
    handleTicketSelection,
    handleSelectAllTickets,
    handleDeselectAllTickets,
    handleTicketClick,
    handleBulkApprove,
    handleBulkReject,
    handleBulkDelete,
    handleApprove,
    handleReject,
    handleCreateTicket,
    handleTicketAction,
    openCreateModal,
    closeCreateModal,
    closeApprovalModal,
    closeDetailModal,
  };
};
