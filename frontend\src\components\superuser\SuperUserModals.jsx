import CreateTicketModal from "../customer/CreateTicketModal";
import TicketDetailModal from "../customer/TicketDetailModal";
import SuperUserTicketDetailModal from "./SuperUserTicketDetailModal";

const SuperUserModals = ({
  showCreateModal,
  showApprovalModal,
  showDetailModal,
  selectedTicket,
  onCloseModals,
  onCreateTicket,
  onApprove,
  onReject,
  onTicketAction,
}) => {
  return (
    <>
      {/* Create Ticket Modal */}
      <CreateTicketModal
        isOpen={showCreateModal}
        onClose={onCloseModals}
        onSubmit={onCreateTicket}
      />

      {/* Approval Modal for Unapproved Tickets */}
      {showApprovalModal && (
        <SuperUserTicketDetailModal
          ticket={selectedTicket}
          isOpen={showApprovalModal}
          onClose={onCloseModals}
          onApprove={onApprove}
          onReject={onReject}
        />
      )}

      {/* Detail Modal for All Tickets and My Tickets */}
      {showDetailModal && (
        <TicketDetailModal
          ticket={selectedTicket}
          isOpen={showDetailModal}
          onClose={onCloseModals}
          onTicketAction={onTicketAction}
        />
      )}
    </>
  );
};

export default SuperUserModals;
