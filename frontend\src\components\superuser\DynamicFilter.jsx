import React, { useState, useRef, useEffect } from 'react';
import { Filter, X } from 'lucide-react';

// Column Filter Dropdown Component
const ColumnFilter = ({ 
  column,
  options,
  value,
  onChange,
  onClose
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  useEffect(() => {
    setIsOpen(true);
  }, []);

  const handleOptionSelect = (option) => {
    onChange(column, option);
    setIsOpen(false);
    onClose();
  };

  const getColumnDisplayName = (col) => {
    const names = {
      category: 'Category',
      priority: 'Priority',
      riskLevel: 'Risk Level',
      customer: 'Customer',
      ticketDetails: 'Ticket Details'
    };
    return names[col] || col;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-48">
          <div className="p-3">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Filter by {getColumnDisplayName(column)}
            </h4>
            <div className="space-y-1 max-h-48 overflow-y-auto">
              {options.map((option) => (
                <button
                  key={option}
                  onClick={() => handleOptionSelect(option)}
                  className={`w-full text-left px-2 py-1.5 text-sm rounded hover:bg-gray-100 transition-colors ${
                    value === option ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Filter Bar Component
const FilterBar = ({ filters, onRemoveFilter, onApplyFilters, onClearAll }) => {
  const getFilterDisplayName = (filterType) => {
    const displayNames = {
      category: 'Category',
      priority: 'Priority',
      riskLevel: 'Risk Level',
      customer: 'Customer',
      ticketDetails: 'Ticket Details'
    };
    return displayNames[filterType] || filterType;
  };

  if (Object.keys(filters).length === 0) return null;

  return (
    <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-700">Active Filters:</h4>
        <div className="flex space-x-2">
          <button
            onClick={onApplyFilters}
            className="px-3 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Apply Filters
          </button>
          <button
            onClick={onClearAll}
            className="px-3 py-1 text-xs bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Clear All
          </button>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        {Object.entries(filters).map(([filterType, value]) => (
          <span
            key={filterType}
            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
          >
            {getFilterDisplayName(filterType)}: {value}
            <button
              onClick={() => onRemoveFilter(filterType)}
              className="ml-1 text-blue-600 hover:text-blue-800"
            >
              <X className="w-3 h-3" />
            </button>
          </span>
        ))}
      </div>
    </div>
  );
};

export { ColumnFilter, FilterBar };
