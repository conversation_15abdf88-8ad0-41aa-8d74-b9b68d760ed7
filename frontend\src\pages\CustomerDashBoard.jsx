import { Plus } from "lucide-react";
import { useState } from "react";
import CreateTicketModal from "../components/customer/CreateTicketModal";
import TicketDetailModal from "../components/customer/TicketDetailModal";
import TicketFilters from "../components/customer/TicketFilters";
import TicketTable from "../components/customer/TicketTable";
import { FilterBar } from "../components/superuser/DynamicFilter";
import { customerFilterOptions, customerTicketData } from "../data/data";

const CustomerDashboard = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Dynamic filter state
  const [pendingFilters, setPendingFilters] = useState({});
  const [appliedFilters, setAppliedFilters] = useState({});
  const [activeFilterColumn, setActiveFilterColumn] = useState(null);

  // Import data from data.js
  const tickets = customerTicketData;
  const filterOptions = customerFilterOptions;

  // Filter management functions
  const handleFilterSelect = (column, value) => {
    setPendingFilters((prev) => ({
      ...prev,
      [column]: value,
    }));
  };

  const handleRemoveFilter = (column) => {
    setPendingFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[column];
      return newFilters;
    });
  };

  const handleApplyFilters = () => {
    setAppliedFilters(pendingFilters);
  };

  const handleClearAllFilters = () => {
    setPendingFilters({});
    setAppliedFilters({});
  };

  // Handler functions
  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowDetailModal(true);
  };

  const handleTicketAction = (action, ticket) => {
    switch (action) {
      case "reopen":
        console.log("Reopening ticket (Continue Chat):", ticket.id);
        // Change status back to "In Progress" and allow new comments
        break;
      case "delete": {
        // Check if ticket can be deleted (within 10 minutes and not seen by super user)
        const now = new Date();
        const timeDiff = (now - ticket.createdTimestamp) / (1000 * 60); // minutes
        if (timeDiff <= 10 && !ticket.seenByAgent) {
          if (window.confirm("Are you sure you want to delete this ticket?")) {
            console.log("Deleting ticket:", ticket.id);
            // Implement delete logic
          }
        } else {
          alert(
            "This ticket cannot be deleted. It's either been seen by an agent or the 10-minute window has passed."
          );
        }
        break;
      }
      case "close":
        if (window.confirm("Are you sure you want to close this ticket?")) {
          console.log("Closing ticket:", ticket.id);
          // Change status to "Closed" - customer initiated closure
        }
        break;
      case "resubmit":
        console.log("Resubmitting rejected ticket:", ticket.id);
        // Open edit modal for rejected ticket
        break;
      default:
        break;
    }
  };

  const handleCreateTicket = async (ticketData) => {
    console.log("Creating new ticket:", ticketData);
    // Create ticket with "Pending Approval" status
    // Max 25MB attachments, max 5 files
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && ticket.status === "Pending Approval") ||
      (activeTab === "active" &&
        ["In Progress", "Seen", "Approved"].includes(ticket.status)) ||
      (activeTab === "resolved" && ticket.status === "Resolved") ||
      (activeTab === "rejected" && ticket.status === "Rejected");

    // Apply dynamic filters
    const matchesFilters = Object.entries(appliedFilters).every(
      ([filterType, filterValue]) => {
        switch (filterType) {
          case "category":
            return ticket.category === filterValue;
          case "status":
            return ticket.status === filterValue;
          case "priority":
            return ticket.priority === filterValue;
          case "agent":
            return (ticket.agent || "Unassigned") === filterValue;
          default:
            return true;
        }
      }
    );

    return matchesSearch && matchesTab && matchesFilters;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                My Support Tickets
              </h1>
              <p className="text-sm text-gray-600">
                Track and manage your support requests
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-3 h-3 mr-1" />
              New Ticket
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        <TicketFilters
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          tickets={tickets}
        />

        {/* Dynamic Filter Bar */}
        <FilterBar
          filters={pendingFilters}
          onRemoveFilter={handleRemoveFilter}
          onApplyFilters={handleApplyFilters}
          onClearAll={handleClearAllFilters}
        />

        {/* Tickets List */}
        <TicketTable
          tickets={filteredTickets}
          onTicketClick={handleTicketClick}
          onTicketAction={handleTicketAction}
          activeFilterColumn={activeFilterColumn}
          setActiveFilterColumn={setActiveFilterColumn}
          pendingFilters={pendingFilters}
          onFilterSelect={handleFilterSelect}
          filterOptions={filterOptions}
        />
      </div>

      {/* Modals */}
      <CreateTicketModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateTicket}
      />

      <TicketDetailModal
        ticket={selectedTicket}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onTicketAction={handleTicketAction}
      />
    </div>
  );
};

export default CustomerDashboard;
