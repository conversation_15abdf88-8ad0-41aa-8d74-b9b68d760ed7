import { Check<PERSON>ircle, XCircle, Plus, Trash2 } from "lucide-react";

const SuperUserHeader = ({
  selectedTickets,
  activeView,
  onCreateTicket,
  onBulkApprove,
  onBulkReject,
  onBulkDelete,
}) => {
  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-3">
          <div>
            <h1 className="text-xl font-bold text-gray-900">
              Ticket Approval Center
            </h1>
            <p className="text-sm text-gray-600">
              Review and approve customer support requests
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onCreateTicket}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-3 h-3 mr-1" />
              New Ticket
            </button>
            {selectedTickets.length > 0 && (
              <>
                {/* Show different bulk actions based on active tab */}
                {activeView === "unapproved" && (
                  <>
                    <button
                      onClick={onBulkApprove}
                      className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Approve ({selectedTickets.length})
                    </button>
                    <button
                      onClick={onBulkReject}
                      className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      <XCircle className="w-3 h-3 mr-1" />
                      Reject ({selectedTickets.length})
                    </button>
                  </>
                )}
                {activeView === "all" && (
                  <button
                    onClick={onBulkDelete}
                    className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    Delete ({selectedTickets.length})
                  </button>
                )}
                {/* No bulk actions for "My Tickets" tab - only individual actions allowed */}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperUserHeader;
